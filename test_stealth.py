#!/usr/bin/env python3
"""
Test script to verify stealth features are working
"""

import sys
import ctypes
from PyQt5.QtWidgets import QApp<PERSON>, QWidget, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt, <PERSON><PERSON><PERSON><PERSON>

def test_stealth_features():
    """Test if stealth features are available on this system"""
    print("🔍 Testing stealth features...")
    
    # Test 1: Check if we can access Windows API functions
    try:
        user32 = ctypes.windll.user32
        print("✅ Windows API access - OK")
    except Exception as e:
        print(f"❌ Windows API access - FAILED: {e}")
        return False
    
    # Test 2: Check if we can create a window and get its handle
    app = QApplication(sys.argv)
    
    window = QWidget()
    window.setWindowTitle("Stealth Test Window")
    window.resize(300, 200)
    
    layout = QVBoxLayout()
    label = QLabel("Testing stealth features...\nThis window should be hidden from screen capture.")
    layout.addWidget(label)
    window.setLayout(layout)
    
    window.show()
    
    try:
        hwnd = int(window.winId())
        print(f"✅ Window handle obtained: {hwnd}")
    except Exception as e:
        print(f"❌ Window handle - FAILED: {e}")
        return False
    
    # Test 3: Test SetWindowDisplayAffinity
    try:
        WDA_EXCLUDEFROMCAPTURE = 0x00000011
        result = user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)
        if result:
            print("✅ SetWindowDisplayAffinity - OK (Window hidden from capture)")
        else:
            print("❌ SetWindowDisplayAffinity - FAILED")
    except Exception as e:
        print(f"❌ SetWindowDisplayAffinity - ERROR: {e}")
    
    # Test 4: Test window style modifications
    try:
        GWL_EXSTYLE = -20
        WS_EX_LAYERED = 0x00080000
        WS_EX_TOOLWINDOW = 0x00000080
        
        current_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_style = current_style | WS_EX_LAYERED | WS_EX_TOOLWINDOW
        result = user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)
        
        if result:
            print("✅ Window style modification - OK")
        else:
            print("❌ Window style modification - FAILED")
    except Exception as e:
        print(f"❌ Window style modification - ERROR: {e}")
    
    # Test 5: Test transparency
    try:
        window.setWindowOpacity(0.8)
        print("✅ Window transparency - OK")
    except Exception as e:
        print(f"❌ Window transparency - ERROR: {e}")
    
    print("\n🎯 Stealth Test Results:")
    print("- Window should be hidden from screen recording software")
    print("- Window should be semi-transparent")
    print("- Window should not appear in taskbar")
    print("\n📋 Try screen recording this window to test!")
    
    # Auto-close after 10 seconds
    QTimer.singleShot(10000, app.quit)
    
    app.exec_()
    return True

if __name__ == "__main__":
    print("🥷 Interview Assistant - Stealth Feature Test")
    print("=" * 50)
    
    if sys.platform != 'win32':
        print("❌ Stealth features only work on Windows")
        sys.exit(1)
    
    test_stealth_features()
    print("\n✅ Stealth test completed!")
