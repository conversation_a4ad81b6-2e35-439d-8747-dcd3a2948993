#!/usr/bin/env python3
"""
Quick test to verify stealth features are working
"""

import sys
import ctypes
from PyQt5.QtWidgets import <PERSON>App<PERSON>, QWidget, QLabel, QVBoxLayout, QPushButton
from PyQt5.QtCore import Qt, QTimer

class StealthTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.apply_stealth()
        
    def initUI(self):
        self.setWindowTitle("🥷 Stealth Test - Should be HIDDEN from screen capture")
        self.setGeometry(200, 200, 400, 300)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("🥷 STEALTH MODE TEST")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: red; background: yellow; padding: 10px;")
        layout.addWidget(title)
        
        # Instructions
        instructions = QLabel("""
🎯 STEALTH TEST INSTRUCTIONS:

1. You can see this window normally
2. Try screen recording/sharing this window
3. This window should be INVISIBLE in the recording
4. Window should be hidden from taskbar
5. Window should be semi-transparent

✅ If window is invisible in screen recording = STEALTH WORKING
❌ If window is visible in screen recording = STEALTH NOT WORKING

Press 'Test Stealth' to apply maximum hiding
        """)
        instructions.setStyleSheet("background: lightblue; padding: 10px; font-family: Consolas;")
        layout.addWidget(instructions)
        
        # Test button
        test_btn = QPushButton("🔒 Test Maximum Stealth")
        test_btn.clicked.connect(self.test_maximum_stealth)
        test_btn.setStyleSheet("background: red; color: white; padding: 10px; font-size: 14px;")
        layout.addWidget(test_btn)
        
        # Close button
        close_btn = QPushButton("❌ Close Test")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("background: gray; color: white; padding: 10px;")
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def apply_stealth(self):
        """Apply stealth features"""
        try:
            hwnd = int(self.winId())
            
            # Hide from screen capture
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result1 = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)
            
            # Apply layered window style
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x00080000
            WS_EX_TOOLWINDOW = 0x00000080
            
            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
            new_style = current_style | WS_EX_LAYERED | WS_EX_TOOLWINDOW
            result2 = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)
            
            # Set transparency
            self.setWindowOpacity(0.85)
            
            print(f"🥷 Stealth applied: Screen capture hiding={result1}, Window styling={result2}")
            print("📹 Try screen recording - this window should be INVISIBLE!")
            
        except Exception as e:
            print(f"❌ Stealth error: {e}")
    
    def test_maximum_stealth(self):
        """Apply maximum stealth"""
        try:
            # Make very transparent
            self.setWindowOpacity(0.3)
            
            # Move to corner
            self.move(50, 50)
            self.resize(300, 200)
            
            print("🔒 Maximum stealth applied!")
            print("📹 Window should be almost invisible in screen recording!")
            
        except Exception as e:
            print(f"❌ Maximum stealth error: {e}")

def main():
    app = QApplication(sys.argv)
    
    print("🥷 Starting Stealth Test...")
    print("📋 This will test if screen capture hiding is working")
    print("📹 Try screen recording while this window is open")
    print("✅ Window should be INVISIBLE in the recording")
    
    window = StealthTestWindow()
    window.show()
    
    # Auto-close after 30 seconds
    QTimer.singleShot(30000, app.quit)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
