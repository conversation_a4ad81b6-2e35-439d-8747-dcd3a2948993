#!/usr/bin/env python3
"""
Test balanced stealth - visible to you, hidden from screen capture
"""

import sys
import ctypes
from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout, QPushButton
from PyQt5.QtCore import Qt, QTimer

class BalancedStealthTest(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.apply_balanced_stealth()
        
    def initUI(self):
        self.setWindowTitle("🎯 Balanced Stealth Test - You CAN see this!")
        self.setGeometry(300, 300, 500, 400)
        
        # Normal window flags - visible to you
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("✅ YOU CAN SEE THIS WINDOW")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: green; background: lightgreen; padding: 15px; border: 3px solid green;")
        layout.addWidget(title)
        
        # Status
        status = QLabel("🎯 BALANCED STEALTH MODE")
        status.setAlignment(Qt.AlignCenter)
        status.setStyleSheet("font-size: 16px; color: blue; background: lightblue; padding: 10px; margin: 10px;")
        layout.addWidget(status)
        
        # Instructions
        instructions = QLabel("""
🎯 BALANCED STEALTH TEST:

✅ What YOU see:
• This window is clearly visible
• Window appears in taskbar
• You can interact normally
• 95% opacity - almost fully visible

🥷 What INTERVIEWER sees in screen share:
• Nothing! Window is hidden from screen capture
• Screen recording will not show this window
• Only screen capture is blocked

📹 TEST INSTRUCTIONS:
1. You should see this window clearly
2. Start screen recording (Win + G)
3. Record this area of screen
4. Play back recording - this window should be INVISIBLE
5. But you can see it right now!

This is PERFECT for interviews! 🚀
        """)
        instructions.setStyleSheet("background: #f0f0f0; padding: 15px; font-family: Consolas; font-size: 12px;")
        layout.addWidget(instructions)
        
        # Test button
        test_btn = QPushButton("📹 I Can See This Button!")
        test_btn.clicked.connect(self.test_click)
        test_btn.setStyleSheet("background: blue; color: white; padding: 15px; font-size: 16px; font-weight: bold;")
        layout.addWidget(test_btn)
        
        # Close button
        close_btn = QPushButton("❌ Close Test")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("background: red; color: white; padding: 10px; font-size: 14px;")
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def apply_balanced_stealth(self):
        """Apply balanced stealth - visible to you, hidden from screen capture"""
        try:
            hwnd = int(self.winId())
            
            # ONLY hide from screen capture - keep everything else normal
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)
            
            # Keep window 95% visible to you
            self.setWindowOpacity(0.95)
            
            if result:
                print("✅ BALANCED STEALTH APPLIED!")
                print("👁️ You can see the window clearly")
                print("🥷 Screen capture will NOT see this window")
                print("📹 Perfect for interviews!")
            else:
                print("⚠️ Screen capture hiding may not be working")
                print("💡 Try running as administrator")
            
        except Exception as e:
            print(f"❌ Stealth error: {e}")
    
    def test_click(self):
        """Test button click"""
        self.sender().setText("✅ Button Works! You can see and click!")
        print("✅ Button clicked - interaction working perfectly!")

def main():
    app = QApplication(sys.argv)
    
    print("🎯 Testing BALANCED STEALTH MODE...")
    print("=" * 50)
    print("✅ Window should be CLEARLY VISIBLE to you")
    print("🥷 Window should be HIDDEN from screen recording")
    print("📹 Test by recording screen - window won't appear in recording")
    print("🎯 This is PERFECT for interviews!")
    print("=" * 50)
    
    window = BalancedStealthTest()
    window.show()
    window.raise_()
    window.activateWindow()
    
    # Auto-close after 60 seconds
    QTimer.singleShot(60000, app.quit)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
