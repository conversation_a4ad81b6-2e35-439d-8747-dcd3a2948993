#!/usr/bin/env python3
"""
Verification script for audio input detection improvements.
"""

import sys
import time
from audio_handler import AudioHandler, RATE, CHUNK, SILENCE_THRESHOLD, SILENT_CHUNKS_TO_STOP

def verify_configuration_improvements():
    """Verify that the audio configuration has been improved."""
    print("🔧 Verifying Audio Configuration Improvements")
    print("=" * 50)
    
    # Check sample rate improvement
    expected_rate = 44100
    if RATE == expected_rate:
        print(f"✅ Sample rate improved: {RATE} Hz (was 16000 Hz)")
    else:
        print(f"❌ Sample rate not improved: {RATE} Hz (expected {expected_rate} Hz)")
    
    # Check chunk size improvement
    expected_chunk = 2048
    if CHUNK == expected_chunk:
        print(f"✅ Chunk size improved: {CHUNK} (was 1024)")
    else:
        print(f"❌ Chunk size not improved: {CHUNK} (expected {expected_chunk})")
    
    # Check silence threshold improvement
    expected_threshold = 200
    if SILENCE_THRESHOLD == expected_threshold:
        print(f"✅ Silence threshold improved: {SILENCE_THRESHOLD} (was 300)")
    else:
        print(f"❌ Silence threshold not improved: {SILENCE_THRESHOLD} (expected {expected_threshold})")
    
    # Check silence duration improvement
    expected_duration = int(4.0 * RATE / CHUNK)
    if SILENT_CHUNKS_TO_STOP == expected_duration:
        silence_seconds = SILENT_CHUNKS_TO_STOP * CHUNK / RATE
        print(f"✅ Silence duration improved: {silence_seconds:.1f}s (was 1.5s)")
    else:
        actual_seconds = SILENT_CHUNKS_TO_STOP * CHUNK / RATE
        expected_seconds = expected_duration * CHUNK / RATE
        print(f"❌ Silence duration not improved: {actual_seconds:.1f}s (expected {expected_seconds:.1f}s)")

def verify_auto_device_detection():
    """Verify that automatic device detection works."""
    print("\n🎤 Verifying Automatic Device Detection")
    print("=" * 50)
    
    try:
        # Create audio handler with auto mode
        audio_handler = AudioHandler(
            app_callback=lambda event_type, data: None,
            selected_device_index_callback=lambda: None,
            auto_device_mode=True
        )
        
        # Test device detection
        current_device = audio_handler.get_current_device_index()
        if current_device is not None:
            print(f"✅ Auto device detection working: Device {current_device}")
            
            # Get device info
            devices = audio_handler.get_available_input_devices()
            device_name = next((name for idx, name in devices if idx == current_device), "Unknown")
            print(f"   Device name: {device_name}")
        else:
            print("❌ Auto device detection failed: No device detected")
        
        # Test device manager
        if audio_handler.device_manager:
            print("✅ Device manager initialized")
        else:
            print("❌ Device manager not initialized")
        
        # Cleanup
        audio_handler.close_audio()
        
    except Exception as e:
        print(f"❌ Auto device detection error: {e}")

def verify_speech_recognition_improvements():
    """Verify speech recognition improvements."""
    print("\n🗣️  Verifying Speech Recognition Improvements")
    print("=" * 50)
    
    try:
        audio_handler = AudioHandler(
            app_callback=lambda event_type, data: None,
            selected_device_index_callback=lambda: None,
            auto_device_mode=True
        )
        
        # Check recognizer settings
        recognizer = audio_handler.recognizer
        
        # Check energy threshold
        if recognizer.energy_threshold == 2000:
            print("✅ Energy threshold improved: 2000 (was 4000)")
        else:
            print(f"❌ Energy threshold not improved: {recognizer.energy_threshold} (expected 2000)")
        
        # Check pause threshold
        if recognizer.pause_threshold == 2.0:
            print("✅ Pause threshold improved: 2.0s (was 1.0s)")
        else:
            print(f"❌ Pause threshold not improved: {recognizer.pause_threshold} (expected 2.0)")
        
        # Check operation timeout
        if recognizer.operation_timeout == 20.0:
            print("✅ Operation timeout improved: 20.0s (was 15.0s)")
        else:
            print(f"❌ Operation timeout not improved: {recognizer.operation_timeout} (expected 20.0)")
        
        # Check if enhanced variables exist
        if hasattr(audio_handler, 'speech_start_time'):
            print("✅ Enhanced speech detection variables added")
        else:
            print("❌ Enhanced speech detection variables missing")
        
        audio_handler.close_audio()
        
    except Exception as e:
        print(f"❌ Speech recognition verification error: {e}")

def verify_ui_improvements():
    """Verify UI improvements by checking main.py structure."""
    print("\n🎨 Verifying UI Improvements")
    print("=" * 50)
    
    try:
        # Check if main.py has the new UI elements
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for device status label
        if 'device_status_label' in content:
            print("✅ Device status label added")
        else:
            print("❌ Device status label missing")
        
        # Check for auto device button improvements
        if '🎤 Auto Detection: ON' in content:
            print("✅ Enhanced auto device button")
        else:
            print("❌ Auto device button not enhanced")
        
        # Check for visibility toggles
        if 'setVisible(False)' in content:
            print("✅ UI visibility controls added")
        else:
            print("❌ UI visibility controls missing")
        
        print("✅ UI improvements verified in source code")
        
    except Exception as e:
        print(f"❌ UI verification error: {e}")

def main():
    """Run all verification tests."""
    print("🔍 Audio Input Detection Improvements Verification")
    print("=" * 60)
    print("This script verifies that all improvements have been implemented correctly.")
    print("=" * 60)
    
    # Run verification tests
    verify_configuration_improvements()
    verify_auto_device_detection()
    verify_speech_recognition_improvements()
    verify_ui_improvements()
    
    print("\n" + "=" * 60)
    print("🏁 Verification Complete!")
    print("=" * 60)
    print("\n📋 Summary:")
    print("✅ = Improvement successfully implemented")
    print("❌ = Issue found - check implementation")
    print("\n🚀 To test the full system, run: python main.py")
    print("🧪 To test audio capture, run: python test_enhanced_audio.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Verification interrupted by user")
    except Exception as e:
        print(f"\n💥 Verification failed: {e}")
        import traceback
        traceback.print_exc()
