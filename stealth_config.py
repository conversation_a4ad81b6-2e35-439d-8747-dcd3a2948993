# Stealth Configuration for Interview Assistant
# This file controls the stealth features of the application

# Window transparency (0.0 = fully transparent, 1.0 = fully opaque)
WINDOW_TRANSPARENCY = 0.95  # Almost fully visible to you

# Enable/disable screen capture hiding
ENABLE_SCREEN_CAPTURE_HIDING = True  # ENABLED - Hidden from screen capture

# Enable/disable taskbar hiding
ENABLE_TASKBAR_HIDING = False  # DISABLED - Visible in taskbar for you

# Enable/disable click-through mode by default
ENABLE_CLICK_THROUGH_DEFAULT = False  # Keep disabled for easier interaction

# Window positioning preferences
WINDOW_POSITION = "bottom-right"  # Options: "bottom-right", "top-right", "bottom-left", "top-left"

# Advanced stealth settings
ADVANCED_STEALTH_MODE = False  # DISABLED - Keep window visible to you
AUTO_HIDE_ON_SCREEN_SHARE = False  # DISABLED - Don't auto-hide window
MINIMIZE_TO_SYSTEM_TRAY = False  # Keep disabled for now

# Hotkey settings
HOTKEY_TOGGLE_VISIBILITY = "ctrl+space"
HOTKEY_TOGGLE_LISTENING = "caps_lock"
HOTKEY_MOVE_WINDOW = "ctrl+arrow_keys"

# UI Settings
COMPACT_MODE = True  # Use smaller, more discrete UI
SHOW_TOOLTIPS = True  # Show helpful tooltips
DARK_MODE = False  # Use dark theme (not implemented yet)

# Debug settings
DEBUG_STEALTH_FEATURES = True  # Print debug messages for stealth features
VERBOSE_LOGGING = False  # Enable detailed logging

print("🎯 BALANCED STEALTH MODE - Visible to You, Hidden from Screen Share!")
print(f"   • Screen capture hiding: {'ON' if ENABLE_SCREEN_CAPTURE_HIDING else 'OFF'}")
print(f"   • Taskbar hiding: {'ON' if ENABLE_TASKBAR_HIDING else 'OFF'}")
print(f"   • Window transparency: {WINDOW_TRANSPARENCY} (95% visible to you)")
print(f"   • Advanced stealth mode: {'ON' if ADVANCED_STEALTH_MODE else 'OFF'}")
print("✅ You can see the window clearly")
print("🥷 Interviewer cannot see it in screen share!")
