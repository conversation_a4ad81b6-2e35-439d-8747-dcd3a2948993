#!/usr/bin/env python3
"""
Test script for enhanced audio input detection and question capture.
"""

import time
import threading
from audio_handler import AudioHandler

class TestAudioCallback:
    def __init__(self):
        self.transcriptions = []
        self.status_messages = []
        self.errors = []
    
    def __call__(self, event_type, data):
        timestamp = time.strftime("%H:%M:%S")
        
        if event_type == "transcription":
            print(f"[{timestamp}] 📝 TRANSCRIPTION: {data}")
            self.transcriptions.append((timestamp, data))
        elif event_type == "status":
            print(f"[{timestamp}] ℹ️  STATUS: {data}")
            self.status_messages.append((timestamp, data))
        elif event_type == "error":
            print(f"[{timestamp}] ❌ ERROR: {data}")
            self.errors.append((timestamp, data))

def test_enhanced_audio_detection():
    """Test the enhanced audio detection system."""
    print("🎤 Enhanced Audio Detection Test")
    print("=" * 60)
    print("This test will:")
    print("1. Automatically detect your default audio input device")
    print("2. Listen for complete interview questions")
    print("3. Use enhanced voice activity detection")
    print("4. Capture longer, more complete audio segments")
    print("=" * 60)
    
    # Create callback handler
    callback = TestAudioCallback()
    
    # Create audio handler with auto device detection
    print("🔧 Initializing enhanced audio handler...")
    audio_handler = AudioHandler(
        app_callback=callback,
        selected_device_index_callback=lambda: None,  # Not used in auto mode
        auto_device_mode=True
    )
    
    # Test device detection
    print("\n🔍 Testing automatic device detection...")
    current_device = audio_handler.get_current_device_index()
    if current_device is not None:
        devices = audio_handler.get_available_input_devices()
        device_name = next((name for idx, name in devices if idx == current_device), "Unknown")
        print(f"✅ Auto-detected device: {current_device} - {device_name}")
    else:
        print("❌ Failed to auto-detect device")
        return False
    
    # Test enhanced listening
    print("\n🎧 Starting enhanced listening test...")
    print("📢 Instructions:")
    print("   - Speak a complete interview question (e.g., 'Tell me about your experience with Python')")
    print("   - The system will wait for you to finish completely before processing")
    print("   - Try pausing mid-sentence to test continuation detection")
    print("   - Press Ctrl+C to stop the test")
    print("\n🎤 Listening for 30 seconds...")
    
    try:
        audio_handler.start_listening()
        
        # Listen for 30 seconds
        start_time = time.time()
        while time.time() - start_time < 30:
            time.sleep(0.5)
            
            # Show progress
            elapsed = int(time.time() - start_time)
            remaining = 30 - elapsed
            print(f"\r⏱️  Listening... {remaining}s remaining", end="", flush=True)
        
        print("\n\n⏹️  Stopping listening...")
        audio_handler.stop_listening()
        
        # Wait a moment for final processing
        time.sleep(2)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        audio_handler.stop_listening()
    
    # Display results
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    print(f"\n📝 Transcriptions captured: {len(callback.transcriptions)}")
    for i, (timestamp, text) in enumerate(callback.transcriptions, 1):
        print(f"   {i}. [{timestamp}] {text}")
    
    print(f"\nℹ️  Status messages: {len(callback.status_messages)}")
    for timestamp, message in callback.status_messages[-5:]:  # Show last 5
        print(f"   [{timestamp}] {message}")
    
    if callback.errors:
        print(f"\n❌ Errors encountered: {len(callback.errors)}")
        for timestamp, error in callback.errors:
            print(f"   [{timestamp}] {error}")
    
    # Cleanup
    audio_handler.close_audio()
    
    # Evaluation
    print("\n🎯 EVALUATION:")
    if callback.transcriptions:
        print("✅ Successfully captured audio transcriptions")
        if len(callback.transcriptions) > 0:
            avg_length = sum(len(text) for _, text in callback.transcriptions) / len(callback.transcriptions)
            print(f"📏 Average transcription length: {avg_length:.1f} characters")
            
            # Check for question indicators
            question_count = sum(1 for _, text in callback.transcriptions 
                               if any(q in text.lower() for q in ['?', 'what', 'how', 'why', 'tell', 'describe']))
            print(f"❓ Detected question-like transcriptions: {question_count}/{len(callback.transcriptions)}")
    else:
        print("❌ No transcriptions captured - check microphone and speak clearly")
    
    if not callback.errors:
        print("✅ No errors encountered")
    
    print("\n🏁 Test completed!")
    return len(callback.transcriptions) > 0

if __name__ == "__main__":
    try:
        success = test_enhanced_audio_detection()
        if success:
            print("\n🎉 Enhanced audio detection test PASSED!")
        else:
            print("\n💥 Enhanced audio detection test FAILED!")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
