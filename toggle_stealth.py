#!/usr/bin/env python3
"""
Toggle between visible and stealth modes
"""

import os

def set_visible_mode():
    """Set application to visible mode for testing"""
    config_content = '''# Stealth Configuration for Interview Assistant
# VISIBLE MODE - For testing and debugging

# Window transparency (0.0 = fully transparent, 1.0 = fully opaque)
WINDOW_TRANSPARENCY = 0.95  # More visible for testing

# Enable/disable screen capture hiding
ENABLE_SCREEN_CAPTURE_HIDING = False  # Disabled for testing

# Enable/disable taskbar hiding
ENABLE_TASKBAR_HIDING = False  # Disabled for testing

# Enable/disable click-through mode by default
ENABLE_CLICK_THROUGH_DEFAULT = False  # Disabled for testing

# Window positioning preferences
WINDOW_POSITION = "bottom-right"

# Advanced stealth settings
ADVANCED_STEALTH_MODE = False  # Disabled for testing
AUTO_HIDE_ON_SCREEN_SHARE = False  # Disabled for testing
MINIMIZE_TO_SYSTEM_TRAY = False

# Hotkey settings
HOTKEY_TOGGLE_VISIBILITY = "ctrl+space"
HOTKEY_TOGGLE_LISTENING = "caps_lock"
HOTKEY_MOVE_WINDOW = "ctrl+arrow_keys"

# UI Settings
COMPACT_MODE = True
SHOW_TOOLTIPS = True
DARK_MODE = False

# Debug settings
DEBUG_STEALTH_FEATURES = True
VERBOSE_LOGGING = True

print("👁️ VISIBLE MODE - Application will be clearly visible")
print("   • Screen capture hiding: OFF")
print("   • Taskbar hiding: OFF")
print("   • Window transparency: 95% (almost opaque)")
print("   • Advanced stealth mode: OFF")
'''
    
    with open('stealth_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Switched to VISIBLE MODE")
    print("📋 Now run: python main.py")

def set_stealth_mode():
    """Set application to full stealth mode"""
    config_content = '''# Stealth Configuration for Interview Assistant
# STEALTH MODE - For actual interview use

# Window transparency (0.0 = fully transparent, 1.0 = fully opaque)
WINDOW_TRANSPARENCY = 0.85

# Enable/disable screen capture hiding
ENABLE_SCREEN_CAPTURE_HIDING = True

# Enable/disable taskbar hiding
ENABLE_TASKBAR_HIDING = True

# Enable/disable click-through mode by default
ENABLE_CLICK_THROUGH_DEFAULT = True

# Window positioning preferences
WINDOW_POSITION = "bottom-right"

# Advanced stealth settings
ADVANCED_STEALTH_MODE = True  # Enables multiple hiding methods
AUTO_HIDE_ON_SCREEN_SHARE = True  # Automatically hide when screen sharing is detected
MINIMIZE_TO_SYSTEM_TRAY = False

# Hotkey settings
HOTKEY_TOGGLE_VISIBILITY = "ctrl+space"
HOTKEY_TOGGLE_LISTENING = "caps_lock"
HOTKEY_MOVE_WINDOW = "ctrl+arrow_keys"

# UI Settings
COMPACT_MODE = True
SHOW_TOOLTIPS = True
DARK_MODE = False

# Debug settings
DEBUG_STEALTH_FEATURES = True
VERBOSE_LOGGING = False

print("🥷 STEALTH MODE - Application will be hidden from screen capture")
print("   • Screen capture hiding: ON")
print("   • Taskbar hiding: ON")
print("   • Window transparency: 85%")
print("   • Advanced stealth mode: ON")
'''
    
    with open('stealth_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Switched to STEALTH MODE")
    print("📋 Now run: python main.py")

def main():
    print("🔧 Interview Assistant - Stealth Mode Switcher")
    print("=" * 50)
    print("1. Visible Mode (for testing)")
    print("2. Stealth Mode (for interviews)")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        set_visible_mode()
    elif choice == '2':
        set_stealth_mode()
    elif choice == '3':
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
