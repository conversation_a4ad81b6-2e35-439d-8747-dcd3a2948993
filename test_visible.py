#!/usr/bin/env python3
"""
Test version of the application that's definitely visible
Use this to test if the application is working before enabling stealth mode
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class VisibleTestApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
    
    def initUI(self):
        # Window setup - VERY VISIBLE
        self.setWindowTitle("🚀 Interview Assistant - VISIBLE TEST MODE")
        self.setGeometry(100, 100, 600, 500)
        
        # Make it very visible
        self.setWindowFlags(Qt.Window)  # Normal window
        self.setWindowOpacity(1.0)  # Fully opaque
        
        # Layout
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("🎯 Interview Assistant - Test Mode")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: red; background-color: yellow; padding: 10px; border: 2px solid red;")
        layout.addWidget(title)
        
        # Status
        status = QLabel("✅ Application is VISIBLE and WORKING!")
        status.setFont(QFont("Arial", 12))
        status.setAlignment(Qt.AlignCenter)
        status.setStyleSheet("color: green; background-color: lightgreen; padding: 10px; margin: 10px;")
        layout.addWidget(status)
        
        # Instructions
        instructions = QTextEdit()
        instructions.setPlainText("""
🎮 CONTROLS TEST:

1. You should be able to see this window clearly
2. Window should be in taskbar
3. You should be able to click and interact normally

🥷 TO ENABLE STEALTH MODE:
1. Close this test application
2. Edit stealth_config.py:
   - Set ENABLE_SCREEN_CAPTURE_HIDING = True
   - Set ENABLE_TASKBAR_HIDING = True
   - Set WINDOW_TRANSPARENCY = 0.85
   - Set ADVANCED_STEALTH_MODE = True
3. Run: python main.py

🔧 BUILD EXECUTABLE:
1. Run: pyinstaller --onefile --windowed main.py
2. Your .exe will be in dist/ folder

📋 CURRENT SETTINGS (Test Mode):
- Screen capture hiding: OFF
- Taskbar hiding: OFF  
- Transparency: OFF
- Click-through: OFF
- Advanced stealth: OFF

This ensures you can see and test the application before making it stealth!
        """)
        instructions.setStyleSheet("background-color: #f0f0f0; padding: 10px; font-family: Consolas;")
        layout.addWidget(instructions)
        
        # Test button
        test_button = QPushButton("🧪 Click to Test - Button Working!")
        test_button.clicked.connect(self.test_click)
        test_button.setStyleSheet("background-color: blue; color: white; padding: 10px; font-size: 14px;")
        layout.addWidget(test_button)
        
        # Close button
        close_button = QPushButton("❌ Close Test Application")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("background-color: red; color: white; padding: 10px; font-size: 14px;")
        layout.addWidget(close_button)
        
        self.setLayout(layout)
        
        # Make sure it's visible
        self.show()
        self.raise_()
        self.activateWindow()
        
        print("🎯 TEST APPLICATION STARTED - Should be VERY VISIBLE!")
        print("📍 Window position: 100, 100")
        print("📏 Window size: 600 x 500")
        print("🔍 Check taskbar - application should be visible there")
    
    def test_click(self):
        print("✅ Button clicked successfully!")
        self.sender().setText("✅ Button Working! Click again to test")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Create and show the test window
    test_window = VisibleTestApp()
    
    print("🚀 Starting VISIBLE test application...")
    print("📋 If you can see this window, the application is working!")
    print("🥷 Use this to test before enabling stealth mode")
    
    sys.exit(app.exec_())
